<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PDF to Image Viewer</title>
  <style>
    body {
      font-family: sans-serif;
      margin: 0;
      padding: 10px;
      background-color: #f5f5f5;
    }
    .container {
      max-width: 100%;
      padding: 10px;
    }
    .pdf-container {
      position: relative;
      width: 100%;
      margin-bottom: 20px;
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 0 8px rgba(0,0,0,0.1);
    }
    .pdf-viewer-container {
      position: relative;
      width: 100%;
      height: 100vh;
      max-height: 800px;
      overflow: hidden;
    }
    .pdf-viewer {
      width: 100%;
      height: 100%;
      border: none;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;
    }
    .pdf-fallback {
      padding: 20px;
      text-align: center;
      background: #f8f8f8;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 15px;
    }
    .pdf-fallback a {
      display: inline-block;
      padding: 10px 20px;
      background: #007bff;
      color: white;
      text-decoration: none;
      border-radius: 5px;
      margin: 10px 0;
    }
    .pdf-image {
      width: 100%;
      height: auto;
      max-width: 100%;
      box-shadow: 0 0 8px rgba(0,0,0,0.1);
      background: white;
      border-radius: 8px;
      transition: transform 0.3s ease;
      cursor: pointer;
      user-select: none;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
    }
    .pdf-image:hover {
      transform: scale(1.02);
    }
    .navigation {
      display: flex;
      justify-content: center;
      gap: 10px;
      margin: 20px 0;
      padding: 10px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 0 8px rgba(0,0,0,0.1);
    }
    .nav-button {
      padding: 8px 16px;
      background: #007bff;
      color: white;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      font-size: 16px;
    }
    .nav-button:disabled {
      background: #ccc;
      cursor: not-allowed;
    }
    .page-number {
      padding: 8px 16px;
      background: white;
      border-radius: 5px;
      box-shadow: 0 0 4px rgba(0,0,0,0.1);
    }
    .image-grid {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 400px;
    }

    #image-preview {
      display: block;
    }

    @media (max-width: 768px) {
      .image-grid {
        padding: 5px;
        min-height: 300px;
      }
      body {
        padding: 5px;
      }
      .pdf-viewer-container {
        height: 70vh;
      }
      .navigation {
        flex-wrap: wrap;
        gap: 5px;
        padding: 8px;
      }
      .nav-button {
        font-size: 14px;
        padding: 8px 12px;
        min-width: 80px;
      }
      .pdf-container {
        margin-bottom: 10px;
      }
      h1, h2 {
        font-size: 18px;
        margin: 10px 0;
      }
      .pdf-image {
        max-width: 100%;
        height: auto;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.15);
      }
      .page-number {
        font-size: 14px;
        padding: 8px 12px;
      }
      .container {
        padding: 5px;
      }
    }

    /* Additional mobile optimizations */
    @media (max-width: 480px) {
      .nav-button {
        font-size: 12px;
        padding: 6px 10px;
        min-width: 70px;
      }
      .page-number {
        font-size: 12px;
        padding: 6px 10px;
      }
      h1 {
        font-size: 16px;
      }
      h2 {
        font-size: 14px;
      }
    }
  </style>
</head>
<body>

  <div class="container">
    <h1>PDF Document</h1>
    
    <!-- PDF Viewer Section -->
    <div class="pdf-container" id="pdf-viewer-section">
      <h2>Original PDF</h2>
      <div class="pdf-viewer-container">
        <iframe
          class="pdf-viewer"
          src="pdf/AnnachiCrackersTariff.pdf#toolbar=0&navpanes=0&scrollbar=1&view=FitH"
          type="application/pdf"
          frameborder="0"
          scrolling="auto"
          id="pdf-iframe"></iframe>
        </iframe>
      </div>
    </div>

    
</body>
</html>
