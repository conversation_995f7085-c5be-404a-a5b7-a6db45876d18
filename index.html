<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PDF to Image Viewer</title>
  <style>
    body {
      font-family: sans-serif;
      margin: 0;
      padding: 10px;
      background-color: #f5f5f5;
    }
    .container {
      max-width: 100%;
      padding: 10px;
    }
    .pdf-container {
      position: relative;
      width: 100%;
      margin-bottom: 20px;
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 0 8px rgba(0,0,0,0.1);
    }
    .pdf-viewer-container {
      position: relative;
      width: 100%;
      height: 100vh;
      max-height: 800px;
      overflow: hidden;
    }
    .pdf-viewer {
      width: 100%;
      height: 100%;
      border: none;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;
    }
    .pdf-fallback {
      padding: 20px;
      text-align: center;
      background: #f8f8f8;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 15px;
    }
    .pdf-fallback a {
      display: inline-block;
      padding: 12px 24px;
      background: #007bff;
      color: white;
      text-decoration: none;
      border-radius: 8px;
      margin: 8px 0;
      font-weight: 500;
      transition: background-color 0.3s ease;
      min-width: 200px;
      text-align: center;
    }
    .pdf-fallback a:hover {
      background: #0056b3;
    }
    .pdf-fallback h3 {
      margin: 0 0 10px 0;
      color: #333;
    }
    .pdf-fallback p {
      margin: 5px 0;
      color: #666;
      line-height: 1.4;
    }
    .pdf-image {
      width: 100%;
      height: auto;
      max-width: 100%;
      box-shadow: 0 0 8px rgba(0,0,0,0.1);
      background: white;
      border-radius: 8px;
      transition: transform 0.3s ease;
      cursor: pointer;
      user-select: none;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
    }
    .pdf-image:hover {
      transform: scale(1.02);
    }
    .navigation {
      display: flex;
      justify-content: center;
      gap: 10px;
      margin: 20px 0;
      padding: 10px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 0 8px rgba(0,0,0,0.1);
    }
    .nav-button {
      padding: 8px 16px;
      background: #007bff;
      color: white;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      font-size: 16px;
    }
    .nav-button:disabled {
      background: #ccc;
      cursor: not-allowed;
    }
    .page-number {
      padding: 8px 16px;
      background: white;
      border-radius: 5px;
      box-shadow: 0 0 4px rgba(0,0,0,0.1);
    }
    .image-grid {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 400px;
    }

    #image-preview {
      display: block;
    }

    @media (max-width: 768px) {
      .image-grid {
        padding: 5px;
        min-height: 300px;
      }
      body {
        padding: 5px;
      }
      .pdf-viewer-container {
        height: 70vh;
      }
      .navigation {
        flex-wrap: wrap;
        gap: 5px;
        padding: 8px;
      }
      .nav-button {
        font-size: 14px;
        padding: 8px 12px;
        min-width: 80px;
      }
      .pdf-container {
        margin-bottom: 10px;
      }
      h1, h2 {
        font-size: 18px;
        margin: 10px 0;
      }
      .pdf-fallback a {
        padding: 14px 20px;
        margin: 10px 0;
        font-size: 16px;
        min-width: 180px;
      }
      .pdf-fallback {
        padding: 15px;
      }
      .pdf-image {
        max-width: 100%;
        height: auto;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.15);
      }
      .page-number {
        font-size: 14px;
        padding: 8px 12px;
      }
      .container {
        padding: 5px;
      }
    }

    /* Additional mobile optimizations */
    @media (max-width: 480px) {
      .nav-button {
        font-size: 12px;
        padding: 6px 10px;
        min-width: 70px;
      }
      .page-number {
        font-size: 12px;
        padding: 6px 10px;
      }
      h1 {
        font-size: 16px;
      }
      h2 {
        font-size: 14px;
      }
    }
  </style>
</head>
<body>

  <div class="container">
    <h1>PDF Document</h1>
    
    <!-- PDF Viewer Section -->
    <div class="pdf-container" id="pdf-viewer-section">
      <h2>PDF Document</h2>
      <div class="pdf-viewer-container" id="pdf-container">
        <!-- Desktop PDF viewer -->
        <iframe
          class="pdf-viewer"
          src="pdf/AnnachiCrackersTariff.pdf#toolbar=0&navpanes=0&scrollbar=1&view=FitH"
          type="application/pdf"
          frameborder="0"
          scrolling="auto"
          id="pdf-iframe"
          style="display: none;"></iframe>

        <!-- Mobile fallback -->
        <div class="pdf-fallback" id="mobile-fallback" style="display: none;">
          <div>
            <h3>📱 Mobile View</h3>
            <p>PDF viewing is not fully supported on mobile browsers.</p>
            <p>Please use one of the options below:</p>
          </div>

          <a href="pdf/AnnachiCrackersTariff.pdf" target="_blank" download>
            📥 Download PDF
          </a>

          <a href="pdf/AnnachiCrackersTariff.pdf" target="_blank">
            🔗 Open in New Tab
          </a>

          <button onclick="tryEmbedPDF()" class="nav-button" style="background: #28a745;">
            🔄 Try Embed View
          </button>
        </div>

        <!-- Alternative embed for some mobile browsers -->
        <div id="embed-container" style="display: none;">
          <embed src="pdf/AnnachiCrackersTariff.pdf" type="application/pdf" width="100%" height="600px" />
        </div>
      </div>
    </div>

    <script>
      // Detect mobile devices and PDF support
      function isMobileDevice() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
      }

      function canDisplayPDF() {
        // Check if browser can display PDFs
        const mimeTypes = navigator.mimeTypes;
        for (let i = 0; i < mimeTypes.length; i++) {
          if (mimeTypes[i].type === 'application/pdf') {
            return true;
          }
        }
        return false;
      }

      function tryEmbedPDF() {
        const embedContainer = document.getElementById('embed-container');
        const mobileContainer = document.getElementById('mobile-fallback');

        mobileContainer.style.display = 'none';
        embedContainer.style.display = 'block';

        // If embed fails after 3 seconds, show fallback again
        setTimeout(() => {
          if (embedContainer.style.display === 'block') {
            const embed = embedContainer.querySelector('embed');
            if (!embed.offsetHeight || embed.offsetHeight < 100) {
              embedContainer.style.display = 'none';
              mobileContainer.style.display = 'block';
              alert('PDF embed not supported. Please use download or open in new tab options.');
            }
          }
        }, 3000);
      }

      // Initialize PDF viewer based on device capabilities
      function initializePDFViewer() {
        const iframe = document.getElementById('pdf-iframe');
        const mobileContainer = document.getElementById('mobile-fallback');

        if (isMobileDevice()) {
          // Mobile device - show fallback options
          iframe.style.display = 'none';
          mobileContainer.style.display = 'block';
        } else {
          // Desktop - try iframe first
          iframe.style.display = 'block';
          mobileContainer.style.display = 'none';

          // Check if iframe loads successfully
          iframe.onload = function() {
            console.log('PDF loaded successfully');
          };

          iframe.onerror = function() {
            console.log('PDF failed to load, showing fallback');
            iframe.style.display = 'none';
            mobileContainer.style.display = 'block';
          };
        }
      }

      // Initialize when page loads
      document.addEventListener('DOMContentLoaded', initializePDFViewer);

      // Handle orientation changes on mobile
      window.addEventListener('orientationchange', function() {
        setTimeout(initializePDFViewer, 500);
      });
    </script>

</body>
</html>
